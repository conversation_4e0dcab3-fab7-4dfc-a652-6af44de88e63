package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"claude-code-api-go/internal/config"
	"claude-code-api-go/internal/database"
	"claude-code-api-go/internal/handlers"
	"claude-code-api-go/internal/managers"
	"claude-code-api-go/internal/middleware"
	"claude-code-api-go/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logrus.Fatalf("Failed to load configuration: %v", err)
	}

	// Configure logging
	setupLogging(cfg)

	logrus.WithField("version", cfg.API.Version).Info("Starting Claude Code API Gateway")

	// Initialize database
	db, err := database.Initialize(cfg.Database.URL)
	if err != nil {
		logrus.Fatalf("Failed to initialize database: %v", err)
	}

	// Auto-migrate database schema
	if err := db.AutoMigrate(&models.Session{}, &models.Message{}, &models.Project{}); err != nil {
		logrus.Fatalf("Failed to migrate database: %v", err)
	}

	logrus.Info("Database initialized")

	// Initialize managers
	sessionManager := managers.NewSessionManager(db)
	claudeManager := managers.NewClaudeManager(cfg)

	logrus.Info("Managers initialized")

	// Verify Claude Code availability
	if err := claudeManager.VerifyClaudeAvailable(); err != nil {
		logrus.WithError(err).Fatal("Claude Code not available")
	}

	version, err := claudeManager.GetVersion()
	if err == nil {
		logrus.WithField("claude_version", version).Info("Claude Code available")
	}

	// Setup router
	if !cfg.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()
	
	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS(cfg.CORS))
	
	if cfg.Auth.RequireAuth {
		router.Use(middleware.Auth(cfg.Auth.APIKeys))
	}

	// Initialize handlers
	h := handlers.New(sessionManager, claudeManager, cfg)

	// Setup routes
	setupRoutes(router, h)

	// Create HTTP server
	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}

	// Start server in goroutine
	go func() {
		logrus.WithFields(logrus.Fields{
			"host": cfg.Server.Host,
			"port": cfg.Server.Port,
		}).Info("Server starting")

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("Shutting down server...")

	// Graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logrus.WithError(err).Error("Server forced to shutdown")
	}

	// Cleanup managers
	claudeManager.Cleanup()

	logrus.Info("Server shutdown complete")
}

func setupLogging(cfg *config.Config) {
	level, err := logrus.ParseLevel(cfg.Log.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)

	if cfg.Log.Format == "json" {
		logrus.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	} else {
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}
}

func setupRoutes(router *gin.Engine, h *handlers.Handlers) {
	// Health check
	router.GET("/health", h.HealthCheck)
	router.GET("/", h.Root)

	// API v1 routes
	v1 := router.Group("/v1")
	{
		// Chat completions (OpenAI compatible)
		v1.POST("/chat/completions", h.CreateChatCompletion)
		v1.GET("/chat/completions/:session_id/status", h.GetCompletionStatus)
		v1.DELETE("/chat/completions/:session_id", h.StopCompletion)

		// Models endpoint
		v1.GET("/models", h.ListModels)

		// Projects management
		projects := v1.Group("/projects")
		{
			projects.GET("", h.ListProjects)
			projects.POST("", h.CreateProject)
			projects.GET("/:id", h.GetProject)
			projects.PUT("/:id", h.UpdateProject)
			projects.DELETE("/:id", h.DeleteProject)
		}

		// Sessions management
		sessions := v1.Group("/sessions")
		{
			sessions.POST("", h.CreateSession)
			sessions.GET("", h.ListSessions)
			sessions.GET("/:id", h.GetSession)
			sessions.GET("/:id/msgs", h.GetSessionMessages)
			sessions.DELETE("/:id", h.DeleteSession)
		}
	}

	// Debug endpoints (only in debug mode)
	if gin.Mode() == gin.DebugMode {
		router.POST("/debug/chat", h.DebugChatCompletion)
	}
}