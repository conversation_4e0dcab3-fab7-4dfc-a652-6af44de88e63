{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Logics Schema", "description": "Schema for logics in Corezoid processes", "type": "array", "items": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["go", "go_if_const", "set_param", "api", "api_callback", "api_sum", "api_code", "api_copy", "api_rpc", "api_rpc_reply", "api_queue", "api_get_task", "api_form", "api_git", "git_call", "db_call"]}}, "allOf": [{"if": {"properties": {"type": {"enum": ["go"]}}}, "then": {"$ref": "#/definitions/go"}}, {"if": {"properties": {"type": {"enum": ["go_if_const"]}}}, "then": {"$ref": "#/definitions/go_if_const"}}, {"if": {"properties": {"type": {"enum": ["set_param"]}}}, "then": {"$ref": "#/definitions/set_param"}}, {"if": {"properties": {"type": {"enum": ["api"]}}}, "then": {"$ref": "#/definitions/api"}}, {"if": {"properties": {"type": {"enum": ["api_callback"]}}}, "then": {"$ref": "#/definitions/api_callback"}}, {"if": {"properties": {"type": {"enum": ["api_sum"]}}}, "then": {"$ref": "#/definitions/api_sum"}}, {"if": {"properties": {"type": {"enum": ["api_code"]}}}, "then": {"$ref": "#/definitions/api_code"}}, {"if": {"properties": {"type": {"enum": ["api_copy"]}}}, "then": {"$ref": "#/definitions/api_copy"}}, {"if": {"properties": {"type": {"enum": ["api_rpc"]}}}, "then": {"$ref": "#/definitions/api_rpc"}}, {"if": {"properties": {"type": {"enum": ["api_rpc_reply"]}}}, "then": {"$ref": "#/definitions/api_rpc_reply"}}, {"if": {"properties": {"type": {"enum": ["api_queue"]}}}, "then": {"$ref": "#/definitions/api_queue"}}, {"if": {"properties": {"type": {"enum": ["api_get_task"]}}}, "then": {"$ref": "#/definitions/api_get_task"}}, {"if": {"properties": {"type": {"enum": ["api_form"]}}}, "then": {"$ref": "#/definitions/api_form"}}, {"if": {"properties": {"type": {"enum": ["api_git", "git_call"]}}}, "then": {"$ref": "#/definitions/api_git"}}, {"if": {"properties": {"type": {"enum": ["db_call"]}}}, "then": {"$ref": "#/definitions/db_call"}}]}, "examples": [[{"type": "go", "to_node_id": "680a3bc3513aa065de6bf632"}], [{"type": "api_copy", "user_id": 68381, "conv_id": 1652722, "ref": "{{root.task_id}}", "mode": "modify", "is_sync": true, "group": "all", "data": {"username": "{{user.name}}", "age": "{{user.age}}", "is_active": "true", "data": "[{\"a\":\"b\"}]"}, "data_type": {"username": "string", "age": "number", "is_active": "boolean", "data": "array"}, "err_node_id": "680a3ce882ba963ebb6b4eb6"}, {"type": "go", "to_node_id": "680b6459513aa065de9f977a"}]]}