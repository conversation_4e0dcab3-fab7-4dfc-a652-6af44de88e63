{"$schema": "http://json-schema.org/draft-07/schema#", "title": "API RPC Logic Schema", "description": "Schema for the api_rpc logic type in Corezoid processes (Call Process)", "type": "object", "required": ["type", "conv_id", "err_node_id"], "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["api_rpc"], "description": "Specifies this is a Call Process (RPC) logic block"}, "err_node_id": {"type": "string", "description": "ID of the node for error handling"}, "extra": {"type": "object", "description": "Parameters to pass to the called process", "default": {}}, "extra_type": {"type": "object", "description": "Data types for the parameters", "default": {}}, "group": {"type": "string", "enum": ["all", ""], "description": "Wait mode for the called process", "default": "all"}, "conv_id": {"type": "integer", "description": "ID of the target Process to call"}, "convTitle": {"type": "string", "description": "Title of the target Process to call"}, "obj_to_id": {"type": ["string", "integer", "null"], "description": "Not typically used for standard calls"}, "is_sync": {"type": "boolean", "description": "Whether to wait for the called process to complete (synchronous call)", "default": true}}, "examples": [{"type": "api_rpc", "err_node_id": "error_condition_node", "extra": {"customer_id": "{{customer_id}}", "amount": "{{amount}}"}, "extra_type": {"customer_id": "string", "amount": "number"}, "group": "all", "conv_id": 1023395, "obj_to_id": null, "is_sync": true}, {"type": "api_rpc", "err_node_id": "error_node_id", "extra": {}, "extra_type": {}, "group": "all", "conv_id": 1023395}]}