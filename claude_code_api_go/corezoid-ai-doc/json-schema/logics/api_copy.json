{"$schema": "http://json-schema.org/draft-07/schema#", "title": "API Copy Logic Schema", "description": "Schema for the api_copy logic type in Corezoid processes", "type": "object", "required": ["type", "mode", "group"], "additionalProperties": false, "properties": {"type": {"type": "string", "enum": ["api_copy"], "description": "Specifies this is a Copy Task logic block"}, "user_id": {"type": "integer", "description": "Internal user ID associated with the operation"}, "conv_id": {"oneOf": [{"type": "integer", "description": "Numeric ID of the target Process"}, {"type": "string", "pattern": "^@.*", "description": "String ID of the target Process, must start with '@'"}], "description": "ID of the target Process to receive the copied task or where the task to modify resides. Can be either a numeric ID or a string starting with '@'"}, "convTitle": {"type": "string", "description": "Title of the target Process to call"}, "ref": {"type": "string", "description": "Reference ID for the task. Can be a static value or a dynamic reference like {{root.task_id}}"}, "mode": {"type": "string", "enum": ["create", "modify"], "description": "Mode for creating a new task or modifying an existing one"}, "is_sync": {"type": "boolean", "description": "Whether to wait for the operation to complete before proceeding (synchronous operation)"}, "group": {"type": "string", "enum": ["all", ""], "description": "Specifies how to handle multiple copies. Must be 'all' when using key/value data in the data parameter. Must be empty string ('') when using empty object data."}, "data": {"type": "object", "description": "Key-value pairs to include in the copied task or to update in the target task", "additionalProperties": {"type": "string", "description": "Value for the parameter, can include dynamic references like {{user.name}}"}}, "data_type": {"type": "object", "description": "Specifies the data types of the copied or updated values", "additionalProperties": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "Data type for the corresponding parameter in the data object"}}, "send_parent_data": {"type": "boolean", "description": "Whether to include the current task's data in the copy (only applicable for mode='create')"}, "err_node_id": {"type": "string", "description": "ID of the node for error handling"}, "obj_to_id": {"type": ["string", "null"], "description": "Not typically used for standard copy operations"}}, "examples": [{"type": "api_copy", "user_id": 68381, "conv_id": 1652722, "ref": "{{root.task_id}}", "mode": "modify", "is_sync": true, "group": "all", "data": {"username": "{{user.name}}", "age": "{{user.age}}", "is_active": "true", "data": "[{\"a\":\"b\"}]"}, "data_type": {"username": "string", "age": "number", "is_active": "boolean", "data": "array"}, "err_node_id": "680a3ce882ba963ebb6b4eb6"}, {"type": "api_copy", "user_id": 68381, "conv_id": "@process_identifier", "ref": "{{root.ref}}", "mode": "create", "group": "all", "data": {"foo": "bar"}, "data_type": {"foo": "string"}, "err_node_id": "680a3c79513aa065de6c2d39"}, {"type": "api_copy", "user_id": 68381, "conv_id": 1652722, "ref": "{{root.ref}}", "mode": "create", "group": "all", "data": {"foo": "bar"}, "data_type": {"foo": "string"}, "err_node_id": "680a3c79513aa065de6c2d39"}]}