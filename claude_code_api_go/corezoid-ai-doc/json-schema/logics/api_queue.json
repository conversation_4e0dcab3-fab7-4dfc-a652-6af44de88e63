{"$schema": "http://json-schema.org/draft-07/schema#", "title": "API Queue Logic Schema", "description": "Schema for the api_queue logic type in Corezoid processes", "type": "object", "additionalProperties": false, "required": ["type"], "properties": {"type": {"type": "string", "enum": ["api_queue"], "description": "Specifies this is a Queue logic block"}, "data": {"type": "object", "description": "Additional parameters for the queue", "default": {}}, "data_type": {"type": "object", "description": "Data types for the parameters", "default": {}}, "capacity": {"type": "integer", "description": "Maximum capacity of the queue", "default": 1000}, "alert_threshold": {"type": "integer", "description": "Threshold for triggering alerts", "default": 900}}, "examples": [{"type": "api_queue", "data": {"key": "val", "key100": "13003"}, "data_type": {"key": "string", "key100": "number"}}, {"type": "api_queue", "data": {"priority": "high", "group": "payments"}, "data_type": {"priority": "string", "group": "string"}, "capacity": 5000, "alert_threshold": 4500}]}