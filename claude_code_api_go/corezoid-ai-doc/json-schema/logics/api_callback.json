{"$schema": "http://json-schema.org/draft-07/schema#", "title": "API Callback Logic Schema", "description": "Schema for the api_callback logic type in Corezoid processes", "type": "object", "additionalProperties": false, "required": ["type"], "properties": {"type": {"type": "string", "enum": ["api_callback"], "description": "Specifies this is a Waiting for Callback logic block"}, "callback_url": {"type": "string", "description": "URL that will be generated for external systems to call back"}, "is_sync": {"type": "boolean", "description": "Whether to wait for the callback synchronously", "default": true}, "obj_id_path": {"type": "string", "description": "Path to the object ID, can be a dynamic reference like {{root.task_id}}"}, "timeout": {"type": "integer", "description": "Callback timeout in seconds", "default": 86400}, "err_node_id": {"type": "string", "description": "ID of the node for error handling on timeout"}, "expected_params": {"type": "array", "description": "List of parameters expected in the callback", "items": {"type": "object", "additionalProperties": false, "required": ["name", "type"], "properties": {"name": {"type": "string", "description": "Parameter name"}, "type": {"type": "string", "enum": ["string", "number", "boolean", "object", "array"], "description": "Parameter data type"}, "required": {"type": "boolean", "description": "Whether the parameter is required"}}}}}, "examples": [{"type": "api_callback", "is_sync": true, "obj_id_path": "{{root.task_id}}"}, {"type": "api_callback", "timeout": 86400, "err_node_id": "timeout_node_id", "expected_params": [{"name": "status", "type": "string", "required": true}, {"name": "transaction_id", "type": "string", "required": true}, {"name": "amount", "type": "number", "required": true}]}]}