##  📋 General Concept
convctl.sh can return various types of errors. Below are all possible errors and their solutions.

### 1.

Error: {"__conveyor_api_return_description__":"Not valid http result json(ok)","__conveyor_api_return_http_code__":200,"__conveyor_api_return_type_tag__":"api_no_valid_json"}

Solution: In this case, this is not an error and the API call can be considered successfully completed