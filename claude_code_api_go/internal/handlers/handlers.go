package handlers

import (
	"claude-code-api-go/internal/config"
	"claude-code-api-go/internal/managers"
	"claude-code-api-go/internal/models"
	"claude-code-api-go/internal/utils"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// Handlers contains all HTTP handlers
type Handlers struct {
	sessionManager *managers.SessionManager
	claudeManager  *managers.ClaudeManager
	config         *config.Config
}

// New creates a new handlers instance
func New(sessionManager *managers.SessionManager, claudeManager *managers.ClaudeManager, cfg *config.Config) *Handlers {
	return &Handlers{
		sessionManager: sessionManager,
		claudeManager:  claudeManager,
		config:         cfg,
	}
}

// HealthCheck handles health check requests
func (h *Handlers) HealthCheck(c *gin.Context) {
	response := models.HealthResponse{
		Status:         "healthy",
		Version:        h.config.API.Version,
		ActiveSessions: len(h.claudeManager.GetActiveSessionIDs()),
	}

	// Check Claude availability
	if version, err := h.claudeManager.GetVersion(); err == nil {
		response.ClaudeVersion = version
	} else {
		response.Status = "unhealthy"
		response.Error = err.Error()
		c.JSON(http.StatusServiceUnavailable, response)
		return
	}

	c.JSON(http.StatusOK, response)
}

// Root handles root endpoint requests
func (h *Handlers) Root(c *gin.Context) {
	response := models.RootResponse{
		Name:        h.config.API.Title,
		Version:     h.config.API.Version,
		Description: h.config.API.Description,
		Endpoints: map[string]string{
			"chat":     "/v1/chat/completions",
			"models":   "/v1/models",
			"projects": "/v1/projects",
			"sessions": "/v1/sessions",
		},
		Docs:   "/docs",
		Health: "/health",
	}

	c.JSON(http.StatusOK, response)
}

// CreateChatCompletion handles chat completion requests
func (h *Handlers) CreateChatCompletion(c *gin.Context) {
	var req models.ChatCompletionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Failed to bind chat completion request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: fmt.Sprintf("Invalid request: %v", err),
				Type:    "invalid_request_error",
			},
		})
		return
	}

	// Extract client info
	clientID := c.GetString("client_id")
	if clientID == "" {
		clientID = "anonymous"
	}

	logrus.WithFields(logrus.Fields{
		"client_id":      clientID,
		"model":          req.Model,
		"messages_count": len(req.Messages),
		"stream":         req.Stream,
		"project_id":     req.ProjectID,
		"session_id":     req.SessionID,
	}).Info("Chat completion request received")

	// Validate model
	claudeModel, err := utils.ValidateClaudeModel(req.Model)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: err.Error(),
				Type:    "invalid_request_error",
				Code:    "invalid_model",
			},
		})
		return
	}

	// Validate messages
	if len(req.Messages) == 0 {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: "At least one message is required",
				Type:    "invalid_request_error",
				Code:    "missing_messages",
			},
		})
		return
	}

	// Extract user prompt
	userMessages := make([]models.ChatMessage, 0)
	for _, msg := range req.Messages {
		if msg.Role == "user" {
			userMessages = append(userMessages, msg)
		}
	}

	if len(userMessages) == 0 {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: "At least one user message is required",
				Type:    "invalid_request_error",
				Code:    "missing_user_message",
			},
		})
		return
	}

	userPrompt := userMessages[len(userMessages)-1].GetTextContent()

	// Extract system prompt
	systemPrompt := req.SystemPrompt
	for _, msg := range req.Messages {
		if msg.Role == "system" {
			systemPrompt = msg.GetTextContent()
			break
		}
	}

	// Handle project context
	projectID := req.ProjectID
	if projectID == "" {
		projectID = fmt.Sprintf("default-%s", clientID)
	}

	projectPath, err := managers.CreateProjectDirectory(projectID, h.config.Project.Root)
	if err != nil {
		logrus.WithError(err).Error("Failed to create project directory")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: models.APIError{
				Message: "Failed to create project directory",
				Type:    "internal_error",
			},
		})
		return
	}

	// Handle session management
	var sessionID string
	if req.SessionID != "" {
		// Continue existing session
		sessionID = req.SessionID
		session, err := h.sessionManager.GetSession(sessionID)
		if err != nil {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: models.APIError{
					Message: fmt.Sprintf("Session %s not found", sessionID),
					Type:    "invalid_request_error",
					Code:    "session_not_found",
				},
			})
			return
		}
		_ = session // Session exists, continue
	} else {
		// Create new session
		sessionID, err = h.sessionManager.CreateSession(projectID, claudeModel, systemPrompt)
		if err != nil {
			logrus.WithError(err).Error("Failed to create session")
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error: models.APIError{
					Message: "Failed to create session",
					Type:    "internal_error",
				},
			})
			return
		}
	}

	// Start Claude Code process
	claudeProcess, err := h.claudeManager.CreateSession(
		sessionID,
		projectPath,
		userPrompt,
		claudeModel,
		systemPrompt,
	)
	if err != nil {
		logrus.WithError(err).Error("Failed to create Claude session")
		c.JSON(http.StatusServiceUnavailable, models.ErrorResponse{
			Error: models.APIError{
				Message: fmt.Sprintf("Failed to start Claude Code: %v", err),
				Type:    "service_unavailable",
				Code:    "claude_unavailable",
			},
		})
		return
	}

	// Use Claude's actual session ID
	actualSessionID := claudeProcess.SessionID

	// Save user message to database
	userMessage := map[string]interface{}{
		"type": "text",
		"text": userPrompt,
	}
	err = h.sessionManager.SaveMessage(
		actualSessionID,
		"user",
		userMessage,
		utils.EstimateTokens(userPrompt),
		0,
	)
	if err != nil {
		logrus.WithError(err).Error("Failed to save user message")
	}

	// Handle streaming vs non-streaming
	if req.Stream {
		// Return streaming response
		c.Header("Cache-Control", "no-cache")
		c.Header("Connection", "keep-alive")
		c.Header("X-Session-ID", actualSessionID)
		c.Header("X-Project-ID", projectID)
		c.Header("Content-Type", "text/plain")

		h.streamResponse(c, actualSessionID, claudeModel, claudeProcess)
	} else {
		// Collect all output for non-streaming response
		response := h.collectNonStreamingResponse(actualSessionID, claudeModel, claudeProcess)
		response.ProjectID = projectID

		c.JSON(http.StatusOK, response)
	}
}

func (h *Handlers) streamResponse(c *gin.Context, sessionID, model string, process *managers.ClaudeProcess) {
	c.Stream(func(w io.Writer) bool {
		select {
		case output, ok := <-process.OutputChan:
			if !ok {
				// Channel closed, send done event
				h.writeSSE(w, "data: [DONE]\n\n")
				return false
			}

			// Save each message immediately
			h.saveIndividualMessage(sessionID, output)

			// Convert Claude output to OpenAI streaming format
			chunk := h.convertToStreamingChunk(sessionID, model, output)
			
			// Also save the streaming chunk if it has finish_reason
			h.saveStreamingChunk(sessionID, chunk)
			
			data, _ := utils.MarshalJSON(chunk)
			h.writeSSE(w, fmt.Sprintf("data: %s\n\n", string(data)))
			return true

		case err, ok := <-process.ErrorChan:
			if ok && err != nil {
				logrus.WithError(err).Error("Claude process error")
				errorChunk := h.createErrorChunk(sessionID, model, err.Error())
				data, _ := utils.MarshalJSON(errorChunk)
				h.writeSSE(w, fmt.Sprintf("data: %s\n\n", string(data)))
			}
			return false

		case <-time.After(30 * time.Second):
			// Timeout
			logrus.WithField("session_id", sessionID).Warning("Streaming timeout")
			return false
		}
	})
}

func (h *Handlers) collectNonStreamingResponse(sessionID, model string, process *managers.ClaudeProcess) models.ChatCompletionResponse {
	var messages []models.ClaudeOutput

	// Collect messages with timeout
	timeout := time.After(time.Duration(h.config.Stream.TimeoutSeconds) * time.Second)

	for {
		select {
		case output, ok := <-process.OutputChan:
			if !ok {
				// Channel closed
				goto done
			}
			messages = append(messages, output)

			if output.Type == "result" {
				goto done
			}

		case err, ok := <-process.ErrorChan:
			if ok && err != nil {
				logrus.WithError(err).Error("Claude process error")
			}
			goto done

		case <-timeout:
			logrus.WithField("session_id", sessionID).Warning("Collection timeout")
			goto done
		}
	}

done:
	// Save each message individually
	for _, msg := range messages {
		h.saveIndividualMessage(sessionID, msg)
	}
	
	// Create OpenAI compatible response
	return h.createChatCompletionResponse(sessionID, model, messages)
}

func (h *Handlers) convertToStreamingChunk(sessionID, model string, output models.ClaudeOutput) models.ChatCompletionResponse {
	// Extract content from Claude message structure
	var content map[string]interface{}

	// Check if this is an assistant message with content
	if output.Type == "assistant" && output.Message != nil {
		if messageContent, ok := output.Message["content"]; ok {
			fmt.Printf("Message content: %+v\n", output.Message)
			// Check if content is an array
			// Handle content array format: [{"type":"text","text":"..."}]
			if contentArray, ok := messageContent.([]interface{}); ok {
				for _, item := range contentArray {
					if itemMap, ok := item.(map[string]interface{}); ok {
						content = itemMap
					}
				}
			} else if contentStr, ok := messageContent.(string); ok {
				// Handle simple string content
				content = map[string]interface{}{
					"type": "text",
					"text": contentStr,
				}
			}
		}
	}

	choice := models.ChatCompletionChoice{
		Index: 0,
		Delta: &models.ChatMessage{
			Role:    "assistant",
			Content: content,
		},
	}

	if output.Type == "result" {
		finishReason := "stop"
		choice.FinishReason = &finishReason
	}

	return models.ChatCompletionResponse{
		ID:      fmt.Sprintf("chatcmpl-%s", uuid.New().String()),
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []models.ChatCompletionChoice{choice},
	}
}

func (h *Handlers) createErrorChunk(sessionID, model, errorMsg string) models.ChatCompletionResponse {
	finishReason := "error"
	choice := models.ChatCompletionChoice{
		Index: 0,
		Delta: &models.ChatMessage{
			Role:    "assistant",
			Content: fmt.Sprintf("Error: %s", errorMsg),
		},
		FinishReason: &finishReason,
	}

	return models.ChatCompletionResponse{
		ID:      fmt.Sprintf("chatcmpl-%s", uuid.New().String()),
		Object:  "chat.completion.chunk",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []models.ChatCompletionChoice{choice},
	}
}

func (h *Handlers) createChatCompletionResponse(sessionID, model string, messages []models.ClaudeOutput) models.ChatCompletionResponse {
	// Combine content from all messages
	var contentParts []string
	var totalTokens int

	for _, msg := range messages {
		// Extract content from Claude message structure
		if msg.Type == "assistant" && msg.Message != nil {
			if messageContent, ok := msg.Message["content"]; ok {
				// Handle content array format: [{"type":"text","text":"..."}]
				if contentArray, ok := messageContent.([]interface{}); ok {
					for _, item := range contentArray {
						if itemMap, ok := item.(map[string]interface{}); ok {
							if itemMap["type"] == "text" {
								if text, ok := itemMap["text"].(string); ok && strings.TrimSpace(text) != "" {
									contentParts = append(contentParts, strings.TrimSpace(text))
								}
							}
						}
					}
				} else if contentStr, ok := messageContent.(string); ok && strings.TrimSpace(contentStr) != "" {
					// Handle simple string content
					contentParts = append(contentParts, strings.TrimSpace(contentStr))
				}
			}
		}

		if usage, ok := msg.Usage["total_tokens"].(float64); ok {
			totalTokens += int(usage)
		}
	}

	// Combine all content parts
	var content string
	if len(contentParts) > 0 {
		content = strings.Join(contentParts, "\n")
	} else {
		content = "Hello! This is a response from Claude Code API Gateway."
	}

	finishReason := "stop"
	choice := models.ChatCompletionChoice{
		Index: 0,
		Message: models.ChatMessage{
			Role:    "assistant",
			Content: content,
		},
		FinishReason: &finishReason,
	}

	return models.ChatCompletionResponse{
		ID:      fmt.Sprintf("chatcmpl-%s", uuid.New().String()),
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   model,
		Choices: []models.ChatCompletionChoice{choice},
		Usage: models.ChatCompletionUsage{
			PromptTokens:     totalTokens / 2,
			CompletionTokens: totalTokens / 2,
			TotalTokens:      totalTokens,
		},
	}
}

func (h *Handlers) writeSSE(w io.Writer, data string) {
	w.Write([]byte(data))
	if flusher, ok := w.(http.Flusher); ok {
		flusher.Flush()
	}
}

// saveIndividualMessage saves each individual message from Claude output
func (h *Handlers) saveIndividualMessage(sessionID string, output models.ClaudeOutput) {
	// Save all messages that have content or are system messages
	if output.Message != nil || output.Type == "result" || output.Type == "system" {
		// Extract tokens and cost
		var tokens int
		var cost float64
		
		if totalTokens, ok := output.Usage["total_tokens"].(float64); ok {
			tokens = int(totalTokens)
		}
		
		cost = output.Cost
		
		// Determine role based on output type
		role := "assistant"
		if output.Type == "system" {
			role = "system"
		}
		
		// Create message content
		var messageContent interface{}
		if output.Message != nil {
			messageContent = output.Message
		} else {
			// For result/stop messages, create a structure with the type info
			messageContent = map[string]interface{}{
				"type":         output.Type,
				"session_id":   output.SessionID,
				"usage":        output.Usage,
				"cost_usd":     output.Cost,
				"duration_ms":  output.Duration,
			}
		}
		
		// Save the message with its complete content
		err := h.sessionManager.SaveMessage(
			sessionID,
			role,
			messageContent,
			tokens,
			cost,
		)
		if err != nil {
			logrus.WithError(err).Error("Failed to save individual message")
		}
	}
}

// saveStreamingChunk saves streaming chunks with finish_reason or other important info
func (h *Handlers) saveStreamingChunk(sessionID string, chunk models.ChatCompletionResponse) {
	// Save chunks that have finish_reason or significant content
	if len(chunk.Choices) > 0 {
		choice := chunk.Choices[0]
		if choice.FinishReason != nil || (choice.Delta != nil && choice.Delta.Content != nil) {
			// Create content for the streaming chunk
			chunkContent := map[string]interface{}{
				"id":      chunk.ID,
				"object":  chunk.Object,
				"created": chunk.Created,
				"model":   chunk.Model,
				"choices": chunk.Choices,
				"usage":   chunk.Usage,
			}
			
			// Determine if this is a finish message
			role := "assistant"
			if choice.FinishReason != nil {
				role = "system"
				chunkContent["finish_reason"] = *choice.FinishReason
			}
			
			// Save the streaming chunk
			err := h.sessionManager.SaveMessage(
				sessionID,
				role,
				chunkContent,
				chunk.Usage.TotalTokens,
				0, // No cost for streaming chunks
			)
			if err != nil {
				logrus.WithError(err).Error("Failed to save streaming chunk")
			}
		}
	}
}

// GetCompletionStatus gets the status of a completion
func (h *Handlers) GetCompletionStatus(c *gin.Context) {
	sessionID := c.Param("session_id")

	session, err := h.sessionManager.GetSession(sessionID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error: models.APIError{
				Message: fmt.Sprintf("Session %s not found", sessionID),
				Type:    "not_found",
				Code:    "session_not_found",
			},
		})
		return
	}

	claudeProcess := h.claudeManager.GetSession(sessionID)
	isRunning := claudeProcess != nil && claudeProcess.IsRunning

	c.JSON(http.StatusOK, gin.H{
		"session_id":    sessionID,
		"project_id":    session.ProjectID,
		"model":         session.Model,
		"is_running":    isRunning,
		"created_at":    session.CreatedAt,
		"updated_at":    session.UpdatedAt,
		"total_tokens":  session.TotalTokens,
		"total_cost":    session.TotalCost,
		"message_count": session.MessageCount,
	})
}

// StopCompletion stops a running completion
func (h *Handlers) StopCompletion(c *gin.Context) {
	sessionID := c.Param("session_id")

	// Stop Claude process
	if err := h.claudeManager.StopSession(sessionID); err != nil {
		logrus.WithError(err).Error("Failed to stop Claude session")
	}

	// End session
	if err := h.sessionManager.EndSession(sessionID); err != nil {
		logrus.WithError(err).Error("Failed to end session")
	}

	c.JSON(http.StatusOK, gin.H{
		"session_id": sessionID,
		"status":     "stopped",
	})
}

// DebugChatCompletion handles debug requests
func (h *Handlers) DebugChatCompletion(c *gin.Context) {
	var req models.ChatCompletionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"status":  "validation_error",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Request validation passed",
		"parsed_data": gin.H{
			"model":          req.Model,
			"messages_count": len(req.Messages),
			"stream":         req.Stream,
		},
	})
}

// ListModels lists available models
func (h *Handlers) ListModels(c *gin.Context) {
	models := []models.Model{
		{
			ID:      "claude-3-5-sonnet-20241022",
			Object:  "model",
			Created: time.Now().Unix(),
			OwnedBy: "anthropic",
		},
		{
			ID:      "claude-3-5-haiku-20241022",
			Object:  "model",
			Created: time.Now().Unix(),
			OwnedBy: "anthropic",
		},
		{
			ID:      "claude-3-opus-20240229",
			Object:  "model",
			Created: time.Now().Unix(),
			OwnedBy: "anthropic",
		},
	}

	c.JSON(http.StatusOK, models)
}

// Project handlers
func (h *Handlers) ListProjects(c *gin.Context) {
	// Simple implementation - in a real app, you'd store projects in the database
	c.JSON(http.StatusOK, []models.Project{})
}

func (h *Handlers) CreateProject(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: err.Error(),
				Type:    "invalid_request_error",
			},
		})
		return
	}

	project := models.Project{
		ID:          uuid.New().String(),
		Name:        req.Name,
		Description: req.Description,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	c.JSON(http.StatusCreated, project)
}

func (h *Handlers) GetProject(c *gin.Context) {
	projectID := c.Param("id")

	// Simple implementation
	project := models.Project{
		ID:        projectID,
		Name:      "Default Project",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	c.JSON(http.StatusOK, project)
}

func (h *Handlers) UpdateProject(c *gin.Context) {
	projectID := c.Param("id")

	var req struct {
		Name        string `json:"name"`
		Description string `json:"description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: err.Error(),
				Type:    "invalid_request_error",
			},
		})
		return
	}

	project := models.Project{
		ID:          projectID,
		Name:        req.Name,
		Description: req.Description,
		UpdatedAt:   time.Now(),
	}

	c.JSON(http.StatusOK, project)
}

func (h *Handlers) DeleteProject(c *gin.Context) {
	projectID := c.Param("id")

	c.JSON(http.StatusOK, gin.H{
		"id":     projectID,
		"status": "deleted",
	})
}

// Session handlers
func (h *Handlers) CreateSession(c *gin.Context) {
	var req models.CreateSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logrus.WithError(err).Error("Failed to bind create session request")
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: fmt.Sprintf("Invalid request: %v", err),
				Type:    "invalid_request_error",
			},
		})
		return
	}

	// Set default model if not provided
	model := req.Model
	if model == "" {
		model = "claude-3-5-sonnet-20241022" // Default model
	}

	// Validate model
	claudeModel, err := utils.ValidateClaudeModel(model)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: models.APIError{
				Message: err.Error(),
				Type:    "invalid_request_error",
				Code:    "invalid_model",
			},
		})
		return
	}

	// Create session
	sessionID, err := h.sessionManager.CreateSession(req.ProjectID, claudeModel, req.SystemPrompt)
	if err != nil {
		logrus.WithError(err).Error("Failed to create session")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: models.APIError{
				Message: "Failed to create session",
				Type:    "internal_error",
			},
		})
		return
	}

	// Get the created session to return full details
	session, err := h.sessionManager.GetSession(sessionID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get created session")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: models.APIError{
				Message: "Failed to retrieve created session",
				Type:    "internal_error",
			},
		})
		return
	}

	logrus.WithFields(logrus.Fields{
		"session_id": sessionID,
		"project_id": req.ProjectID,
		"model":      claudeModel,
	}).Info("Session created successfully")

	c.JSON(http.StatusCreated, session)
}

func (h *Handlers) ListSessions(c *gin.Context) {
	projectID := c.Query("project_id")
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	sessions, err := h.sessionManager.ListSessions(projectID, limit, offset)
	if err != nil {
		logrus.WithError(err).Error("Failed to list sessions")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: models.APIError{
				Message: "Failed to list sessions",
				Type:    "internal_error",
			},
		})
		return
	}

	c.JSON(http.StatusOK, sessions)
}

func (h *Handlers) GetSession(c *gin.Context) {
	sessionID := c.Param("id")

	session, err := h.sessionManager.GetSession(sessionID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error: models.APIError{
				Message: fmt.Sprintf("Session %s not found", sessionID),
				Type:    "not_found",
				Code:    "session_not_found",
			},
		})
		return
	}

	// Get messages for the session
	messages, err := h.sessionManager.GetSessionMessages(sessionID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get session messages")
	} else {
		session.Messages = messages
	}

	c.JSON(http.StatusOK, session)
}

func (h *Handlers) DeleteSession(c *gin.Context) {
	sessionID := c.Param("id")

	// Stop Claude process if running
	if err := h.claudeManager.StopSession(sessionID); err != nil {
		logrus.WithError(err).Error("Failed to stop Claude session")
	}

	// Delete session from database
	if err := h.sessionManager.DeleteSession(sessionID); err != nil {
		logrus.WithError(err).Error("Failed to delete session")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: models.APIError{
				Message: "Failed to delete session",
				Type:    "internal_error",
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":     sessionID,
		"status": "deleted",
	})
}

// GetSessionMessages retrieves all messages for a session
func (h *Handlers) GetSessionMessages(c *gin.Context) {
	sessionID := c.Param("id")

	// Check if session exists
	session, err := h.sessionManager.GetSession(sessionID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Error: models.APIError{
				Message: fmt.Sprintf("Session %s not found", sessionID),
				Type:    "not_found",
				Code:    "session_not_found",
			},
		})
		return
	}

	// Get messages for the session
	messages, err := h.sessionManager.GetSessionMessages(sessionID)
	if err != nil {
		logrus.WithError(err).Error("Failed to get session messages")
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: models.APIError{
				Message: "Failed to retrieve session messages",
				Type:    "internal_error",
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"session_id": sessionID,
		"project_id": session.ProjectID,
		"messages":   messages,
		"count":      len(messages),
	})
}
