package database

import (
	"strings"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize creates and configures the database connection
func Initialize(databaseURL string) (*gorm.DB, error) {
	// Parse database URL (simplified - only supports SQLite for now)
	var dialector gorm.Dialector
	
	if strings.HasPrefix(databaseURL, "sqlite://") {
		// Remove sqlite:// prefix and handle file paths correctly
		dbPath := strings.TrimPrefix(databaseURL, "sqlite://")
		// Remove leading slash if it's a relative path (sqlite:///./file.db -> ./file.db)
		if strings.HasPrefix(dbPath, "/./") {
			dbPath = strings.TrimPrefix(dbPath, "/")
		}
		dialector = sqlite.Open(dbPath)
	} else {
		// Default to SQLite
		dialector = sqlite.Open(databaseURL)
	}
	
	// Configure GORM
	config := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Reduce log noise
	}
	
	db, err := gorm.Open(dialector, config)
	if err != nil {
		return nil, err
	}
	
	return db, nil
}

// Close closes the database connection
func Close(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}