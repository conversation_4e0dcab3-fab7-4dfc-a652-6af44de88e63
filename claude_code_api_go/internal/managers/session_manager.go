package managers

import (
	"claude-code-api-go/internal/models"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// SessionManager handles session management
type SessionManager struct {
	db *gorm.DB
}

// NewSessionManager creates a new session manager
func NewSessionManager(db *gorm.DB) *SessionManager {
	return &SessionManager{db: db}
}

// CreateSession creates a new chat session
func (sm *SessionManager) CreateSession(projectID, model, systemPrompt string) (string, error) {
	session := &models.Session{
		ID:           uuid.New().String(),
		ProjectID:    projectID,
		Model:        model,
		SystemPrompt: systemPrompt,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		IsActive:     true,
	}

	if err := sm.db.Create(session).Error; err != nil {
		return "", err
	}

	return session.ID, nil
}

// GetSession retrieves a session by ID
func (sm *SessionManager) GetSession(sessionID string) (*models.Session, error) {
	var session models.Session
	if err := sm.db.Where("id = ?", sessionID).First(&session).Error; err != nil {
		return nil, err
	}
	return &session, nil
}

// UpdateSession updates session with new message and usage stats
func (sm *SessionManager) UpdateSession(sessionID, messageContent, role string, tokens int, cost float64) error {
	// Create message record
	message := &models.Message{
		SessionID: sessionID,
		Role:      role,
		Content:   messageContent,
		Tokens:    tokens,
		Cost:      cost,
		CreatedAt: time.Now(),
	}

	if err := sm.db.Create(message).Error; err != nil {
		return err
	}

	// Update session totals
	return sm.db.Model(&models.Session{}).Where("id = ?", sessionID).Updates(map[string]interface{}{
		"total_tokens":  gorm.Expr("total_tokens + ?", tokens),
		"total_cost":    gorm.Expr("total_cost + ?", cost),
		"message_count": gorm.Expr("message_count + 1"),
		"updated_at":    time.Now(),
	}).Error
}

// EndSession marks a session as inactive
func (sm *SessionManager) EndSession(sessionID string) error {
	return sm.db.Model(&models.Session{}).Where("id = ?", sessionID).Updates(map[string]interface{}{
		"is_active":  false,
		"updated_at": time.Now(),
	}).Error
}

// ListSessions returns all sessions for a project
func (sm *SessionManager) ListSessions(projectID string, limit, offset int) ([]models.Session, error) {
	var sessions []models.Session
	query := sm.db.Model(&models.Session{})
	
	if projectID != "" {
		query = query.Where("project_id = ?", projectID)
	}
	
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&sessions).Error; err != nil {
		return nil, err
	}
	
	return sessions, nil
}

// GetSessionMessages returns all messages for a session
func (sm *SessionManager) GetSessionMessages(sessionID string) ([]models.Message, error) {
	var messages []models.Message
	if err := sm.db.Where("session_id = ?", sessionID).Order("created_at ASC").Find(&messages).Error; err != nil {
		return nil, err
	}
	return messages, nil
}

// DeleteSession deletes a session and all its messages
func (sm *SessionManager) DeleteSession(sessionID string) error {
	// Delete messages first
	if err := sm.db.Where("session_id = ?", sessionID).Delete(&models.Message{}).Error; err != nil {
		return err
	}
	
	// Delete session
	return sm.db.Where("id = ?", sessionID).Delete(&models.Session{}).Error
}

// CleanupExpiredSessions removes old inactive sessions
func (sm *SessionManager) CleanupExpiredSessions(maxAge time.Duration) error {
	cutoff := time.Now().Add(-maxAge)
	
	// Get expired session IDs
	var sessionIDs []string
	if err := sm.db.Model(&models.Session{}).
		Where("is_active = false AND updated_at < ?", cutoff).
		Pluck("id", &sessionIDs).Error; err != nil {
		return err
	}
	
	// Delete messages for expired sessions
	if len(sessionIDs) > 0 {
		if err := sm.db.Where("session_id IN ?", sessionIDs).Delete(&models.Message{}).Error; err != nil {
			return err
		}
		
		// Delete expired sessions
		if err := sm.db.Where("id IN ?", sessionIDs).Delete(&models.Session{}).Error; err != nil {
			return err
		}
	}
	
	return nil
}