package managers

import (
	"bufio"
	"claude-code-api-go/internal/config"
	"claude-code-api-go/internal/models"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ClaudeProcess represents a running Claude Code process
type ClaudeProcess struct {
	SessionID   string
	ProjectPath string
	Cmd         *exec.Cmd
	IsRunning   bool
	OutputChan  chan models.ClaudeOutput
	ErrorChan   chan error
	ctx         context.Context
	cancel      context.CancelFunc
	mu          sync.RWMutex
}

// NewClaudeProcess creates a new Claude process
func NewClaudeProcess(sessionID, projectPath string) *ClaudeProcess {
	ctx, cancel := context.WithCancel(context.Background())
	return &ClaudeProcess{
		SessionID:   sessionID,
		ProjectPath: projectPath,
		OutputChan:  make(chan models.ClaudeOutput, 100),
		ErrorChan:   make(chan error, 10),
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start starts the Claude Code process
func (cp *ClaudeProcess) Start(binaryPath, prompt, model, systemPrompt string) error {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	if cp.IsRunning {
		return fmt.Errorf("process already running")
	}

	// Build command
	args := []string{"-p", prompt}
	
	if systemPrompt != "" {
		args = append(args, "--system-prompt", systemPrompt)
	}
	
	if model != "" {
		args = append(args, "--model", model)
	}
	
	args = append(args, "--output-format", "stream-json", "--verbose", "--dangerously-skip-permissions")

	// Create command with context
	cp.Cmd = exec.CommandContext(cp.ctx, binaryPath, args...)
	cp.Cmd.Dir = cp.ProjectPath

	// Set up pipes
	stdout, err := cp.Cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderr, err := cp.Cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start the process
	if err := cp.Cmd.Start(); err != nil {
		return fmt.Errorf("failed to start Claude process: %w", err)
	}

	cp.IsRunning = true

	// Start output reading goroutines
	go cp.readOutput(stdout.(*os.File))
	go cp.readErrors(stderr.(*os.File))
	
	// Wait for process completion
	go func() {
		defer func() {
			cp.mu.Lock()
			cp.IsRunning = false
			cp.mu.Unlock()
			close(cp.OutputChan)
			close(cp.ErrorChan)
		}()
		
		if err := cp.Cmd.Wait(); err != nil {
			select {
			case cp.ErrorChan <- err:
			case <-cp.ctx.Done():
			}
		}
	}()

	logrus.WithFields(logrus.Fields{
		"session_id":   cp.SessionID,
		"project_path": cp.ProjectPath,
		"model":        model,
	}).Info("Claude process started")

	return nil
}

func (cp *ClaudeProcess) readOutput(stdout *os.File) {
	scanner := bufio.NewScanner(stdout)
	for scanner.Scan() {
		line := scanner.Text()
		if line == "" {
			continue
		}

		var output models.ClaudeOutput
		if err := json.Unmarshal([]byte(line), &output); err != nil {
			// Handle non-JSON output
			output = models.ClaudeOutput{
				Type:    "text",
				Content: line,
			}
		}

		// Update session ID if provided by Claude
		if output.SessionID != "" {
			cp.SessionID = output.SessionID
		}

		select {
		case cp.OutputChan <- output:
		case <-cp.ctx.Done():
			return
		}
	}
}

func (cp *ClaudeProcess) readErrors(stderr *os.File) {
	scanner := bufio.NewScanner(stderr)
	for scanner.Scan() {
		line := scanner.Text()
		if line != "" {
			select {
			case cp.ErrorChan <- fmt.Errorf("claude stderr: %s", line):
			case <-cp.ctx.Done():
				return
			}
		}
	}
}

// Stop stops the Claude process
func (cp *ClaudeProcess) Stop() error {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	if !cp.IsRunning {
		return nil
	}

	cp.cancel()
	
	if cp.Cmd != nil && cp.Cmd.Process != nil {
		// Give it a chance to exit gracefully
		done := make(chan error, 1)
		go func() {
			done <- cp.Cmd.Wait()
		}()

		select {
		case <-time.After(5 * time.Second):
			// Force kill if it doesn't exit gracefully
			cp.Cmd.Process.Kill()
		case <-done:
		}
	}

	cp.IsRunning = false
	
	logrus.WithField("session_id", cp.SessionID).Info("Claude process stopped")
	return nil
}

// ClaudeManager manages multiple Claude processes
type ClaudeManager struct {
	config    *config.Config
	processes map[string]*ClaudeProcess
	mu        sync.RWMutex
}

// NewClaudeManager creates a new Claude manager
func NewClaudeManager(cfg *config.Config) *ClaudeManager {
	return &ClaudeManager{
		config:    cfg,
		processes: make(map[string]*ClaudeProcess),
	}
}

// VerifyClaudeAvailable checks if Claude binary is available
func (cm *ClaudeManager) VerifyClaudeAvailable() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, cm.config.Claude.BinaryPath, "--version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("Claude binary not available at %s: %w", cm.config.Claude.BinaryPath, err)
	}
	return nil
}

// GetVersion gets Claude CLI version
func (cm *ClaudeManager) GetVersion() (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, cm.config.Claude.BinaryPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return string(output), nil
}

// CreateSession creates a new Claude session
func (cm *ClaudeManager) CreateSession(sessionID, projectPath, prompt, model, systemPrompt string) (*ClaudeProcess, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// Check concurrent session limit
	if len(cm.processes) >= cm.config.Claude.MaxConcurrentSessions {
		return nil, fmt.Errorf("maximum concurrent sessions (%d) reached", cm.config.Claude.MaxConcurrentSessions)
	}

	// Ensure project directory exists
	if err := os.MkdirAll(projectPath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create project directory: %w", err)
	}

	// Create process
	process := NewClaudeProcess(sessionID, projectPath)

	// Start process
	if err := process.Start(
		cm.config.Claude.BinaryPath,
		prompt,
		model,
		systemPrompt,
	); err != nil {
		return nil, err
	}

	// Don't store long-running processes for CLI that completes immediately
	// This prevents "max concurrent sessions" errors
	
	logrus.WithFields(logrus.Fields{
		"session_id":      process.SessionID,
		"active_sessions": len(cm.processes),
	}).Info("Claude session created")

	return process, nil
}

// GetSession retrieves an existing session
func (cm *ClaudeManager) GetSession(sessionID string) *ClaudeProcess {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.processes[sessionID]
}

// StopSession stops a session
func (cm *ClaudeManager) StopSession(sessionID string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	process, exists := cm.processes[sessionID]
	if !exists {
		return nil // Already stopped or doesn't exist
	}

	if err := process.Stop(); err != nil {
		return err
	}

	delete(cm.processes, sessionID)
	
	logrus.WithFields(logrus.Fields{
		"session_id":      sessionID,
		"active_sessions": len(cm.processes),
	}).Info("Claude session stopped")

	return nil
}

// Cleanup stops all sessions
func (cm *ClaudeManager) Cleanup() {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	for sessionID, process := range cm.processes {
		if err := process.Stop(); err != nil {
			logrus.WithError(err).WithField("session_id", sessionID).Error("Failed to stop Claude process")
		}
	}

	cm.processes = make(map[string]*ClaudeProcess)
	logrus.Info("All Claude sessions cleaned up")
}

// GetActiveSessionIDs returns list of active session IDs
func (cm *ClaudeManager) GetActiveSessionIDs() []string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	ids := make([]string, 0, len(cm.processes))
	for id := range cm.processes {
		ids = append(ids, id)
	}
	return ids
}

// CreateProjectDirectory creates a project directory
func CreateProjectDirectory(projectID, projectRoot string) (string, error) {
	projectPath := fmt.Sprintf("%s/%s", projectRoot, projectID)
	if err := os.MkdirAll(projectPath, 0755); err != nil {
		return "", fmt.Errorf("failed to create project directory: %w", err)
	}
	return projectPath, nil
}