package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Session represents a chat session
type Session struct {
	ID           string    `json:"id" gorm:"primaryKey"`
	ProjectID    string    `json:"project_id" gorm:"index"`
	Model        string    `json:"model"`
	SystemPrompt string    `json:"system_prompt,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	TotalTokens  int       `json:"total_tokens"`
	TotalCost    float64   `json:"total_cost"`
	MessageCount int       `json:"message_count"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	
	Messages []Message `json:"messages,omitempty" gorm:"foreignKey:SessionID"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (s *Session) BeforeCreate(tx *gorm.DB) error {
	if s.ID == "" {
		s.ID = uuid.New().String()
	}
	return nil
}

// Message represents a single message in a conversation
type Message struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	SessionID string    `json:"session_id" gorm:"index"`
	Role      string    `json:"role"` // "user", "assistant", "system"
	Content   string    `json:"content"`
	Tokens    int       `json:"tokens"`
	Cost      float64   `json:"cost"`
	CreatedAt time.Time `json:"created_at"`
	
	Session Session `json:"-" gorm:"foreignKey:SessionID"`
}

// Project represents a project workspace
type Project struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	Path        string    `json:"path"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	Sessions []Session `json:"sessions,omitempty" gorm:"foreignKey:ProjectID"`
}

// BeforeCreate will set a UUID rather than numeric ID
func (p *Project) BeforeCreate(tx *gorm.DB) error {
	if p.ID == "" {
		p.ID = uuid.New().String()
	}
	return nil
}

// OpenAI API compatible structures

// ChatCompletionRequest represents an OpenAI chat completion request
type ChatCompletionRequest struct {
	Model            string        `json:"model" binding:"required"`
	Messages         []ChatMessage `json:"messages" binding:"required,min=1"`
	Temperature      *float64      `json:"temperature,omitempty"`
	TopP             *float64      `json:"top_p,omitempty"`
	N                *int          `json:"n,omitempty"`
	Stream           bool          `json:"stream,omitempty"`
	Stop             interface{}   `json:"stop,omitempty"`
	MaxTokens        *int          `json:"max_tokens,omitempty"`
	PresencePenalty  *float64      `json:"presence_penalty,omitempty"`
	FrequencyPenalty *float64      `json:"frequency_penalty,omitempty"`
	LogitBias        interface{}   `json:"logit_bias,omitempty"`
	User             string        `json:"user,omitempty"`
	
	// Extensions for Claude Code API
	ProjectID     string `json:"project_id,omitempty"`
	SessionID     string `json:"session_id,omitempty"`
	SystemPrompt  string `json:"system_prompt,omitempty"`
}

// ChatMessage represents a message in the conversation
type ChatMessage struct {
	Role    string      `json:"role" binding:"required,oneof=system user assistant"`
	Content interface{} `json:"content" binding:"required"`
	Name    string      `json:"name,omitempty"`
}

// GetTextContent extracts text content from the message
func (m *ChatMessage) GetTextContent() string {
	switch content := m.Content.(type) {
	case string:
		return content
	case []interface{}:
		// Handle array of content parts (multimodal)
		for _, part := range content {
			if partMap, ok := part.(map[string]interface{}); ok {
				if partMap["type"] == "text" {
					if text, ok := partMap["text"].(string); ok {
						return text
					}
				}
			}
		}
	}
	return ""
}

// ChatCompletionResponse represents an OpenAI chat completion response
type ChatCompletionResponse struct {
	ID                string                 `json:"id"`
	Object            string                 `json:"object"`
	Created           int64                  `json:"created"`
	Model             string                 `json:"model"`
	Choices           []ChatCompletionChoice `json:"choices"`
	Usage             ChatCompletionUsage    `json:"usage"`
	SystemFingerprint string                 `json:"system_fingerprint,omitempty"`
	
	// Extensions
	ProjectID string `json:"project_id,omitempty"`
	SessionID string `json:"session_id,omitempty"`
}

// ChatCompletionChoice represents a single choice in the response
type ChatCompletionChoice struct {
	Index        int               `json:"index"`
	Message      ChatMessage       `json:"message,omitempty"`
	Delta        *ChatMessage      `json:"delta,omitempty"`
	FinishReason *string           `json:"finish_reason"`
	Logprobs     interface{}       `json:"logprobs,omitempty"`
}

// ChatCompletionUsage represents token usage information
type ChatCompletionUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// Model represents an available model
type Model struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	OwnedBy string `json:"owned_by"`
}

// ModelsResponse represents the response for listing models
type ModelsResponse struct {
	Object string  `json:"object"`
	Data   []Model `json:"data"`
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error APIError `json:"error"`
}

// APIError represents API error details
type APIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code,omitempty"`
}

// Claude specific structures

// ClaudeOutput represents output from Claude CLI
type ClaudeOutput struct {
	Type      string                 `json:"type"`
	SessionID string                 `json:"session_id,omitempty"`
	Message   map[string]interface{} `json:"message,omitempty"`
	Usage     map[string]interface{} `json:"usage,omitempty"`
	Cost      float64                `json:"cost_usd,omitempty"`
	Duration  int                    `json:"duration_ms,omitempty"`
	Content   string                 `json:"content,omitempty"`
}

// Health check response
type HealthResponse struct {
	Status         string `json:"status"`
	Version        string `json:"version"`
	ClaudeVersion  string `json:"claude_version,omitempty"`
	ActiveSessions int    `json:"active_sessions"`
	Error          string `json:"error,omitempty"`
}

// CreateSessionRequest represents a request to create a new session
type CreateSessionRequest struct {
	ProjectID    string `json:"project_id" binding:"required"`
	Model        string `json:"model,omitempty"`
	SystemPrompt string `json:"system_prompt,omitempty"`
	SessionID    string `json:"session_id,omitempty"`
}

// Root endpoint response
type RootResponse struct {
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Description string            `json:"description"`
	Endpoints   map[string]string `json:"endpoints"`
	Docs        string            `json:"docs"`
	Health      string            `json:"health"`
}