package config

import (
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/spf13/viper"
)

type Config struct {
	API      APIConfig      `mapstructure:"api"`
	Server   ServerConfig   `mapstructure:"server"`
	Auth     AuthConfig     `mapstructure:"auth"`
	<PERSON>fig   `mapstructure:"claude"`
	Project  ProjectConfig  `mapstructure:"project"`
	Database DatabaseConfig `mapstructure:"database"`
	Log      LogConfig      `mapstructure:"log"`
	CORS     CORSConfig     `mapstructure:"cors"`
	Rate     RateConfig     `mapstructure:"rate"`
	Stream   StreamConfig   `mapstructure:"stream"`
	Debug    bool           `mapstructure:"debug"`
}

type APIConfig struct {
	Title       string `mapstructure:"title"`
	Version     string `mapstructure:"version"`
	Description string `mapstructure:"description"`
}

type ServerConfig struct {
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
}

type AuthConfig struct {
	APIKeys     []string `mapstructure:"api_keys"`
	RequireAuth bool     `mapstructure:"require_auth"`
}

type ClaudeConfig struct {
	BinaryPath            string `mapstructure:"binary_path"`
	APIKey                string `mapstructure:"api_key"`
	DefaultModel          string `mapstructure:"default_model"`
	MaxConcurrentSessions int    `mapstructure:"max_concurrent_sessions"`
	SessionTimeoutMinutes int    `mapstructure:"session_timeout_minutes"`
}

type ProjectConfig struct {
	Root                 string `mapstructure:"root"`
	MaxSizeMB            int    `mapstructure:"max_size_mb"`
	CleanupIntervalMins  int    `mapstructure:"cleanup_interval_minutes"`
}

type DatabaseConfig struct {
	URL string `mapstructure:"url"`
}

type LogConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

type CORSConfig struct {
	AllowedOrigins []string `mapstructure:"allowed_origins"`
	AllowedMethods []string `mapstructure:"allowed_methods"`
	AllowedHeaders []string `mapstructure:"allowed_headers"`
}

type RateConfig struct {
	RequestsPerMinute int `mapstructure:"requests_per_minute"`
	Burst             int `mapstructure:"burst"`
}

type StreamConfig struct {
	ChunkSize      int `mapstructure:"chunk_size"`
	TimeoutSeconds int `mapstructure:"timeout_seconds"`
}

func Load() (*Config, error) {
	// Set default values
	viper.SetDefault("api.title", "Claude Code API Gateway")
	viper.SetDefault("api.version", "1.0.0")
	viper.SetDefault("api.description", "OpenAI-compatible API for Claude Code")
	
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8000)
	
	viper.SetDefault("auth.require_auth", false)
	viper.SetDefault("auth.api_keys", []string{})
	
	viper.SetDefault("claude.binary_path", findClaudeBinary())
	viper.SetDefault("claude.api_key", "")
	viper.SetDefault("claude.default_model", "claude-3-5-sonnet-20241022")
	viper.SetDefault("claude.max_concurrent_sessions", 10)
	viper.SetDefault("claude.session_timeout_minutes", 30)
	
	viper.SetDefault("project.root", "/Users/<USER>/work/sources/corezoid-ai-doc")
	viper.SetDefault("project.max_size_mb", 1000)
	viper.SetDefault("project.cleanup_interval_minutes", 60)
	
	viper.SetDefault("database.url", "sqlite:///./claude_api.db")
	
	viper.SetDefault("log.level", "INFO")
	viper.SetDefault("log.format", "json")
	
	viper.SetDefault("cors.allowed_origins", []string{"*"})
	viper.SetDefault("cors.allowed_methods", []string{"*"})
	viper.SetDefault("cors.allowed_headers", []string{"*"})
	
	viper.SetDefault("rate.requests_per_minute", 100)
	viper.SetDefault("rate.burst", 10)
	
	viper.SetDefault("stream.chunk_size", 1024)
	viper.SetDefault("stream.timeout_seconds", 300)
	
	viper.SetDefault("debug", false)

	// Read from environment variables
	viper.AutomaticEnv()
	viper.SetEnvPrefix("CLAUDE_API")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Read from config file if exists
	viper.SetConfigName(".env")
	viper.SetConfigType("env")
	viper.AddConfigPath(".")
	viper.ReadInConfig() // Ignore errors for missing config file

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	// Ensure project root exists
	os.MkdirAll(config.Project.Root, 0755)

	return &config, nil
}

func findClaudeBinary() string {
	// Check environment variable first
	if path := os.Getenv("CLAUDE_BINARY_PATH"); path != "" {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	// Try to find in PATH
	if path, err := exec.LookPath("claude"); err == nil {
		return path
	}

	// Try npm global bin path
	if cmd := exec.Command("npm", "bin", "-g"); cmd != nil {
		if output, err := cmd.Output(); err == nil {
			npmBin := strings.TrimSpace(string(output))
			claudePath := filepath.Join(npmBin, "claude")
			if _, err := os.Stat(claudePath); err == nil {
				return claudePath
			}
		}
	}

	// Common fallback locations
	commonPaths := []string{
		"/usr/local/bin/claude",
		"/usr/bin/claude",
		"/opt/homebrew/bin/claude",
	}

	for _, path := range commonPaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	return "claude" // Final fallback
}