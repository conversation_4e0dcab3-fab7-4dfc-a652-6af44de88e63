# Multi-stage build for Claude Code API Gateway (Go)

# Build stage
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata gcc musl-dev sqlite-dev

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./


# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build \
    -ldflags="-s -w" \
    -trimpath \
    -o claude-api-server \
    cmd/main.go

# Runtime stage
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    sqlite \
    nodejs \
    npm

# Install Claude Code CLI (mock for testing)
RUN echo '#!/bin/sh' > /usr/local/bin/claude && \
    echo 'echo "Claude CLI mock - version 1.0.0"' >> /usr/local/bin/claude && \
    echo 'exit 0' >> /usr/local/bin/claude && \
    chmod +x /usr/local/bin/claude

# Create application user
RUN addgroup -g 1001 -S claude && \
    adduser -u 1001 -S claude -G claude

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/claude-api-server .

# Create directories for projects and database
RUN mkdir -p /tmp/claude_projects && \
    chown -R claude:claude /app /tmp/claude_projects

# Switch to non-root user
USER claude

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8000/health || exit 1

# Run the application
CMD ["./claude-api-server"]